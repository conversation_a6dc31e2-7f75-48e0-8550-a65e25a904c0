# Gravity Forms Elementor Widget

A powerful Elementor widget that provides enhanced customization options for Gravity Forms with seamless integration between Gravity Forms field settings and Elementor styling controls.

## Features

### 🎨 Enhanced Customization Options
- **Dynamic Field-Based Controls**: Styling options automatically adapt based on the fields present in your selected form
- **Form Settings Integration**: Option to respect CSS classes and settings configured in Gravity Forms
- **Field-Type Specific Styling**: Separate styling controls for different field types (text, email, textarea, select, etc.)
- **Responsive Design**: Built-in responsive styling for mobile devices

### 🔧 Gravity Forms Integration
- **Automatic Form Detection**: Dynamically loads available Gravity Forms
- **Field Settings Respect**: Honors CSS classes, field sizes, and other settings from Gravity Forms
- **Enhanced CSS Classes**: Automatically adds Elementor-specific classes for better targeting
- **Form-Level Customization**: Supports form-level CSS classes and configurations

### 📱 Improved User Experience
- **Organized Controls**: Styling options are logically grouped by field type
- **Conditional Controls**: Only shows relevant styling options based on your form's fields
- **Better Descriptions**: Clear descriptions for each control option
- **Backward Compatibility**: Maintains compatibility with existing widget configurations

## Installation

1. Upload the plugin folder to `/wp-content/plugins/gravityfromelementor/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. The widget will appear in Elementor under "Impact Hub Elements" and "Gravity Forms" categories

## Usage

### Basic Setup
1. Add the "Gravity Form" widget to your Elementor page
2. Select your desired form from the dropdown
3. Configure basic options (title, description, AJAX)

### Enhanced Customization
1. **Enable Form Settings Integration**: Toggle "Use Form Field Settings" to respect Gravity Forms configurations
2. **Dynamic Styling**: The widget automatically creates styling sections based on your form's field types
3. **Field-Specific Controls**: Each field type gets its own styling section with relevant options

### Available Controls

#### Content Tab
- **Form Selection**: Choose from available Gravity Forms
- **Form Settings Integration**: Toggle to use Gravity Forms field settings
- **Title & Description**: Control visibility and spacing
- **AJAX Settings**: Enable/disable AJAX form submission
- **Label Display**: Control how field labels are displayed
- **Fieldset Options**: Show/hide fieldset borders

#### Style Tab
- **Dynamic Field Sections**: Automatically generated based on form fields
  - Text Fields (text, email, phone, website, number)
  - Textarea Fields
  - Select/Dropdown Fields
  - Choice Fields (radio, checkbox)
- **Legacy Styling Sections**: Backward-compatible styling options
- **Submit Button**: Comprehensive button styling
- **Section Breaks**: Styling for form sections

## Technical Features

### Dynamic Control Generation
The widget analyzes your selected form and creates appropriate styling controls:

```php
// Automatically detects field types in your form
$field_types = $this->get_form_field_types($form_id);

// Creates styling sections for each field type
foreach ($field_types as $field_type) {
    $this->register_field_type_controls($field_type, $fields);
}
```

### Form Settings Integration
When enabled, the widget:
- Respects CSS classes set in Gravity Forms
- Honors field size settings (small, medium, large)
- Adds enhanced CSS classes for better Elementor integration
- Maintains form-level styling configurations

### CSS Class Structure
The widget adds several CSS classes for enhanced targeting:

- `.gf-widget`: Base widget class
- `.gf-use-form-settings`: When form settings integration is enabled
- `.gf-has-{field_type}`: Indicates presence of specific field types
- `.elementor-gf-field`: Enhanced field class
- `.elementor-gf-{field_type}`: Field-type specific class
- `.elementor-gf-size-{size}`: Size-specific styling

## Customization Examples

### Targeting Specific Field Types
```css
/* Style all text inputs */
.gf-widget .elementor-gf-text input {
    border-radius: 8px;
    border: 2px solid #e1e5e9;
}

/* Style textareas differently */
.gf-widget .elementor-gf-textarea textarea {
    min-height: 120px;
    resize: vertical;
}
```

### Responsive Styling
```css
/* Mobile-specific adjustments */
@media (max-width: 768px) {
    .gf-widget .gform_wrapper input,
    .gf-widget .gform_wrapper textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}
```

## Changelog

### Version 1.1.0 (Latest)
- ✨ Added dynamic field-based styling controls
- ✨ Implemented Gravity Forms settings integration
- ✨ Enhanced CSS class structure for better targeting
- ✨ Added responsive styling improvements
- ✨ Improved control organization and descriptions
- 🐛 Fixed various styling inconsistencies
- 🔧 Maintained backward compatibility

### Version 1.0.2
- Initial release with basic Gravity Forms integration
- Custom checkbox/radio styling
- Basic field styling options

## Requirements

- WordPress 5.0+
- Elementor 3.0+
- Gravity Forms 2.5+
- PHP 7.4+

## Support

For support and feature requests, please contact Impact Hub or create an issue in the project repository.

## License

This plugin is licensed under the GPL v2 or later.
