<?php
/**
 * Plugin Name: Gravity Form Elementor Widget
 * Description: Enhanced Elementor widget for Gravity Forms with dynamic field-based styling and form settings integration
 * Version:     1.1.0
 * Author:      Impact Hub
 * Author URI:  https://impacthub.net
 * Text Domain: elementor-addon
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('GF_ELEMENTOR_VERSION', '1.1.0');
define('GF_ELEMENTOR_PLUGIN_URL', plugin_dir_url(__FILE__));
define('GF_ELEMENTOR_PLUGIN_PATH', plugin_dir_path(__FILE__));

// Check if Gravity Forms is active
add_action('admin_init', 'gf_elementor_check_dependencies');

function gf_elementor_check_dependencies() {
    if (!class_exists('GFForms')) {
        add_action('admin_notices', 'gf_elementor_gravity_forms_missing_notice');
        deactivate_plugins(plugin_basename(__FILE__));
    }

    if (!did_action('elementor/loaded')) {
        add_action('admin_notices', 'gf_elementor_elementor_missing_notice');
        deactivate_plugins(plugin_basename(__FILE__));
    }
}

function gf_elementor_gravity_forms_missing_notice() {
    echo '<div class="notice notice-error"><p>';
    echo '<strong>Gravity Form Elementor Widget</strong> requires Gravity Forms to be installed and activated.';
    echo '</p></div>';
}

function gf_elementor_elementor_missing_notice() {
    echo '<div class="notice notice-error"><p>';
    echo '<strong>Gravity Form Elementor Widget</strong> requires Elementor to be installed and activated.';
    echo '</p></div>';
}

add_action( 'elementor/widgets/register', 'register_gravity_form_elementor_widget' );

function register_gravity_form_elementor_widget( $widgets_manager ) {
    // Check if Gravity Forms is available
    if (!class_exists('GFAPI')) {
        return;
    }

	require_once( __DIR__ . '/widgets/gf-widget.php' );
	$widgets_manager->register( new \Elementor_GF_Widget() );
}

function gf_register_widget_styles() {
	wp_register_style(
        'gf-widget',
        plugins_url( 'assets/css/style.css', __FILE__ ),
        array(),
        GF_ELEMENTOR_VERSION
    );
    wp_enqueue_style( 'gf-widget' );
}
add_action( 'wp_enqueue_scripts', 'gf_register_widget_styles' );

// Add admin styles for better Elementor editor experience
function gf_register_admin_styles() {
    if (isset($_GET['action']) && $_GET['action'] === 'elementor') {
        wp_enqueue_style( 'gf-widget' );
    }
}
add_action( 'admin_enqueue_scripts', 'gf_register_admin_styles' );