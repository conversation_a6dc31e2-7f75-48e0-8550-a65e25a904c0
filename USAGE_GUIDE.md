# Gravity Forms Elementor Widget - Usage Guide

## Quick Start

### 1. Basic Setup
1. **Add the Widget**: In Elementor, search for "Gravity Form" and add it to your page
2. **Select Form**: Choose your Gravity Form from the dropdown
3. **Configure Basic Options**: Set title, description, and AJAX preferences

### 2. Enable Enhanced Features
- **Toggle "Use Form Field Settings"**: This enables integration with your Gravity Forms field configurations
- **Dynamic Styling Sections**: The widget will automatically create styling sections based on your form's field types

## Key Features Explained

### 🎯 Form Settings Integration

When you enable "Use Form Field Settings", the widget will:
- Respect CSS classes you've set in Gravity Forms
- Honor field size settings (small, medium, large)
- Add enhanced CSS classes for better Elementor targeting
- Maintain form-level styling configurations

**Example**: If you set a CSS class "highlight-field" on a text field in Gravity Forms, it will be preserved and enhanced with additional Elementor-specific classes.

### 🎨 Dynamic Field-Based Controls

The widget analyzes your selected form and creates appropriate styling controls:

- **Text Fields**: Styling for text, email, phone, website, number fields
- **Textarea Fields**: Specific controls for textarea elements
- **Select Fields**: Dropdown-specific styling options
- **Choice Fields**: Radio button and checkbox styling
- **And more**: The widget adapts to whatever field types are in your form

### 📱 Responsive Design

Built-in responsive features include:
- Mobile-optimized field sizing
- Touch-friendly input areas
- Responsive spacing and layout
- iOS zoom prevention (16px minimum font size)

## Advanced Customization

### Using CSS Classes

The widget adds several CSS classes you can target:

```css
/* Target all enhanced fields */
.elementor-gf-field { }

/* Target specific field types */
.elementor-gf-text { }
.elementor-gf-email { }
.elementor-gf-textarea { }
.elementor-gf-select { }

/* Target field sizes */
.elementor-gf-size-small { }
.elementor-gf-size-medium { }
.elementor-gf-size-large { }

/* Target forms using settings integration */
.gf-use-form-settings { }

/* Target forms with specific field types */
.gf-has-text { }
.gf-has-email { }
.gf-has-textarea { }
```

### Custom CSS Examples

#### Modern Input Styling
```css
.gf-widget input[type="text"],
.gf-widget input[type="email"] {
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.gf-widget input:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}
```

#### Custom Button Styling
```css
.gf-widget .gform_footer input[type="submit"] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 14px 32px;
    color: white;
    font-weight: 600;
    transition: transform 0.2s ease;
}

.gf-widget .gform_footer input[type="submit"]:hover {
    transform: translateY(-2px);
}
```

## Best Practices

### 1. Form Configuration in Gravity Forms
- Set appropriate field sizes (small, medium, large) in Gravity Forms
- Use CSS classes in Gravity Forms for fields that need special styling
- Configure field labels and descriptions properly
- Test your form functionality before styling

### 2. Elementor Widget Configuration
- Always select your form first before configuring styling
- Enable "Use Form Field Settings" for better integration
- Use the dynamic field sections for field-type specific styling
- Test responsive behavior on different devices

### 3. Custom CSS
- Use the provided CSS classes for better targeting
- Test your custom CSS with different form configurations
- Consider mobile users when adding custom styling
- Use CSS custom properties for consistent theming

### 4. Performance
- Minimize custom CSS when possible
- Use the widget's built-in controls instead of overriding with CSS
- Test form submission functionality after styling changes

## Troubleshooting

### Common Issues

**Q: My styling controls aren't showing up**
A: Make sure you've selected a form first. The dynamic controls are generated based on your form's field types.

**Q: Form settings integration isn't working**
A: Ensure "Use Form Field Settings" is enabled and that your form has CSS classes or size settings configured in Gravity Forms.

**Q: Custom CSS isn't applying**
A: Check CSS specificity. The widget uses specific selectors, so you may need to use more specific selectors or `!important`.

**Q: Form looks different in editor vs frontend**
A: This is normal. The Elementor editor may not show all styling. Always test on the frontend.

### Getting Help

1. Check the browser console for JavaScript errors
2. Verify Gravity Forms and Elementor are up to date
3. Test with a default theme to rule out theme conflicts
4. Check if other plugins are interfering

## Migration from Previous Versions

If you're upgrading from version 1.0.2:

1. **Backup your site** before updating
2. **Review your existing widgets** - they should continue working
3. **Enable new features** by toggling "Use Form Field Settings"
4. **Explore dynamic controls** - new styling sections will appear based on your forms
5. **Update custom CSS** if needed to use new CSS classes

The plugin maintains backward compatibility, so existing configurations will continue to work.

## Examples and Templates

Check the `/examples/` folder for:
- `custom-styling.css` - Complete styling examples
- Additional templates and configurations

## Support

For additional support:
- Review the main README.md file
- Check Gravity Forms documentation
- Consult Elementor documentation
- Contact Impact Hub for plugin-specific issues
