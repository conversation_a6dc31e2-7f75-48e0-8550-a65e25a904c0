/**
 * Custom Styling Examples for Gravity Forms Elementor Widget
 * 
 * This file contains examples of how to leverage the enhanced CSS classes
 * and form settings integration for advanced customization.
 */

/* ===== BASIC FORM STYLING ===== */

/* Style the main form container */
.gf-widget {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* ===== FIELD TYPE SPECIFIC STYLING ===== */

/* Text Input Fields (text, email, phone, website, number) */
.gf-widget .elementor-gf-text input,
.gf-widget .elementor-gf-email input,
.gf-widget .elementor-gf-phone input,
.gf-widget .elementor-gf-website input,
.gf-widget .elementor-gf-number input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: #ffffff;
}

.gf-widget .elementor-gf-text input:focus,
.gf-widget .elementor-gf-email input:focus,
.gf-widget .elementor-gf-phone input:focus,
.gf-widget .elementor-gf-website input:focus,
.gf-widget .elementor-gf-number input:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
    outline: none;
}

/* Textarea Fields */
.gf-widget .elementor-gf-textarea textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    font-family: inherit;
    resize: vertical;
    transition: all 0.3s ease;
}

.gf-widget .elementor-gf-textarea textarea:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
    outline: none;
}

/* Select/Dropdown Fields */
.gf-widget .elementor-gf-select select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    background-color: #ffffff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    appearance: none;
    cursor: pointer;
}

.gf-widget .elementor-gf-select select:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
    outline: none;
}

/* ===== CHOICE FIELDS (RADIO & CHECKBOX) ===== */

/* Radio Button Styling */
.gf-widget .elementor-gf-radio .gchoice {
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.gf-widget .elementor-gf-radio input[type="radio"] {
    width: 20px;
    height: 20px;
    margin: 0;
    accent-color: #007cba;
}

.gf-widget .elementor-gf-radio label {
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
    cursor: pointer;
}

/* Checkbox Styling */
.gf-widget .elementor-gf-checkbox .gchoice {
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.gf-widget .elementor-gf-checkbox input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin: 0;
    accent-color: #007cba;
}

.gf-widget .elementor-gf-checkbox label {
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
    cursor: pointer;
}

/* ===== SIZE-SPECIFIC STYLING ===== */

/* Small Fields */
.gf-widget .elementor-gf-size-small input,
.gf-widget .elementor-gf-size-small textarea,
.gf-widget .elementor-gf-size-small select {
    padding: 8px 12px;
    font-size: 14px;
}

/* Medium Fields (default) */
.gf-widget .elementor-gf-size-medium input,
.gf-widget .elementor-gf-size-medium textarea,
.gf-widget .elementor-gf-size-medium select {
    padding: 12px 16px;
    font-size: 16px;
}

/* Large Fields */
.gf-widget .elementor-gf-size-large input,
.gf-widget .elementor-gf-size-large textarea,
.gf-widget .elementor-gf-size-large select {
    padding: 16px 20px;
    font-size: 18px;
}

/* ===== FORM LABELS ===== */

.gf-widget .gfield_label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #374151;
    font-size: 16px;
}

.gf-widget .gfield_required {
    color: #dc2626;
}

/* ===== SUBMIT BUTTON ===== */

.gf-widget .gform_footer input[type="submit"] {
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    color: white;
    border: none;
    padding: 14px 32px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.gf-widget .gform_footer input[type="submit"]:hover {
    background: linear-gradient(135deg, #005a87 0%, #004066 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 124, 186, 0.3);
}

.gf-widget .gform_footer input[type="submit"]:active {
    transform: translateY(0);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .gf-widget {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    .gf-widget .gform_wrapper input,
    .gf-widget .gform_wrapper textarea,
    .gf-widget .gform_wrapper select {
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .gf-widget .gform_footer input[type="submit"] {
        width: 100%;
        padding: 16px;
    }
}

/* ===== FORM VALIDATION STYLING ===== */

.gf-widget .gfield_error input,
.gf-widget .gfield_error textarea,
.gf-widget .gfield_error select {
    border-color: #dc2626 !important;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.gf-widget .validation_error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.gf-widget .gfield_error .validation_message {
    color: #dc2626;
    font-size: 14px;
    margin-top: 6px;
}

/* ===== CUSTOM FORM THEMES ===== */

/* Dark Theme Example */
.gf-widget.dark-theme {
    background: #1f2937;
    color: #f9fafb;
}

.gf-widget.dark-theme input,
.gf-widget.dark-theme textarea,
.gf-widget.dark-theme select {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

.gf-widget.dark-theme input:focus,
.gf-widget.dark-theme textarea:focus,
.gf-widget.dark-theme select:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.gf-widget.dark-theme .gfield_label {
    color: #f9fafb;
}
